<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-gray-900">短剧分析剪辑</h1>
            </div>
            <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
              <router-link v-for="item in navigation" :key="item.name" :to="item.href"
                class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                :class="{
                  'border-primary-500 text-primary-600': $route.path === item.href
                }">
                <component :is="item.icon" class="w-4 h-4 mr-2" />
                {{ item.name }}
              </router-link>
            </div>
          </div>

          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 未登录状态 -->
            <div v-if="!authStore.isAuthenticated" class="flex items-center space-x-2">
              <router-link to="/login"
                class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                登录
              </router-link>
              <router-link to="/register"
                class="bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                注册
              </router-link>
            </div>

            <!-- 已登录状态 -->
            <div v-else class="flex items-center space-x-4">
              <!-- 通知按钮 -->
              <button type="button"
                class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <span class="sr-only">查看通知</span>
                <BellIcon class="h-6 w-6" />
              </button>

              <!-- 用户下拉菜单 -->
              <div class="relative" ref="userMenuRef">
                <button @click="showUserMenu = !showUserMenu"
                  class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                  <span class="sr-only">打开用户菜单</span>
                  <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                    <UserIcon class="h-5 w-5 text-gray-600" />
                  </div>
                  <span class="ml-2 text-gray-700 font-medium">
                    {{ authStore.user?.full_name || authStore.user?.username }}
                  </span>
                  <ChevronDownIcon class="ml-1 h-4 w-4 text-gray-500" />
                </button>

                <!-- 下拉菜单 -->
                <div v-show="showUserMenu"
                  class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                  <div class="py-1">
                    <router-link to="/profile" @click="showUserMenu = false"
                      class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <UserIcon class="mr-3 h-4 w-4" />
                      个人中心
                    </router-link>
                    <div v-if="authStore.isAdmin" class="border-t border-gray-100">
                      <router-link to="/admin/users" @click="showUserMenu = false"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <UsersIcon class="mr-3 h-4 w-4" />
                        用户管理
                      </router-link>
                    </div>
                    <div class="border-t border-gray-100">
                      <button @click="handleLogout"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <ArrowRightOnRectangleIcon class="mr-3 h-4 w-4" />
                        登出
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="flex-1">
      <router-view />
    </main>

    <!-- 全局加载指示器 -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
        <span class="text-gray-900">处理中...</span>
      </div>
    </div>

    <!-- 全局通知 -->
    <div v-if="notification.show" class="fixed top-4 right-4 z-50 max-w-sm w-full">
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4" :class="{
        'border-green-200 bg-green-50': notification.type === 'success',
        'border-red-200 bg-red-50': notification.type === 'error',
        'border-yellow-200 bg-yellow-50': notification.type === 'warning',
        'border-blue-200 bg-blue-50': notification.type === 'info'
      }">
        <div class="flex">
          <div class="flex-shrink-0">
            <CheckCircleIcon v-if="notification.type === 'success'" class="h-5 w-5 text-green-400" />
            <XCircleIcon v-else-if="notification.type === 'error'" class="h-5 w-5 text-red-400" />
            <ExclamationTriangleIcon v-else-if="notification.type === 'warning'" class="h-5 w-5 text-yellow-400" />
            <InformationCircleIcon v-else class="h-5 w-5 text-blue-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">
              {{ notification.title }}
            </p>
            <p v-if="notification.message" class="mt-1 text-sm text-gray-500">
              {{ notification.message }}
            </p>
          </div>
          <div class="ml-auto pl-3">
            <button @click="hideNotification" class="inline-flex text-gray-400 hover:text-gray-500">
              <XMarkIcon class="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  BellIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
  FolderIcon,
  VideoCameraIcon,
  ScissorsIcon,
  PencilSquareIcon,
  UserIcon,
  UsersIcon,
  ChevronDownIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline'

import { useAppStore } from '@/stores/app'
import { useAuthStore } from '@/stores/auth'
import { initializeStoreRef } from '@/api/client'

const router = useRouter()
const appStore = useAppStore()
const authStore = useAuthStore()

// 用户菜单状态
const showUserMenu = ref(false)
const userMenuRef = ref(null)

// 导航菜单
const navigation = [
  { name: '任务管理', href: '/tasks', icon: FolderIcon },
  // { name: '视频分析', href: '/analysis', icon: VideoCameraIcon },
  { name: '片段管理', href: '/clips', icon: ScissorsIcon },
  { name: '剪辑工作台', href: '/editor', icon: PencilSquareIcon },
]

// 计算属性
const isLoading = computed(() => appStore.isLoading)
const notification = computed(() => appStore.notification)

// 方法
const hideNotification = () => {
  appStore.hideNotification()
}

const handleLogout = async () => {
  showUserMenu.value = false
  await authStore.logout()
  router.push('/login')
}

// 点击外部关闭用户菜单
const handleClickOutside = (event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target)) {
    showUserMenu.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 初始化 HTTP client 的 store 引用
  initializeStoreRef(appStore)

  // 初始化认证状态
  await authStore.initAuth()

  // 添加点击外部事件监听
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style>
/* 主色调样式 */
.bg-primary-600 {
  background-color: rgb(37 99 235);
}

.bg-primary-700 {
  background-color: rgb(29 78 216);
}

.hover\:bg-primary-700:hover {
  background-color: rgb(29 78 216);
}

.text-primary-600 {
  color: rgb(37 99 235);
}

.border-primary-500 {
  border-color: rgb(59 130 246);
}

.ring-primary-500 {
  --tw-ring-color: rgb(59 130 246);
}

.focus\:ring-primary-500:focus {
  --tw-ring-color: rgb(59 130 246);
}
</style>
