<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端应用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .nav {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav h1 {
            margin: 0;
            color: #333;
        }
        .nav .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .hidden {
            display: none;
        }
        .login-form {
            max-width: 400px;
            margin: 50px auto;
            padding: 30px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: white;
        }
        .login-form input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        .tasks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .task-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }
        .task-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .task-status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-processing {
            background: #d1ecf1;
            color: #0c5460;
        }
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav">
        <h1>短剧分析剪辑</h1>
        <div class="user-info">
            <span id="userInfo" class="hidden"></span>
            <button id="logoutBtn" class="danger hidden" onclick="logout()">登出</button>
            <button id="loginBtn" onclick="showLogin()">登录</button>
        </div>
    </div>

    <!-- 登录表单 -->
    <div id="loginForm" class="login-form hidden">
        <h2>用户登录</h2>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="admin123456">
        <button onclick="login()" style="width: 100%;">登录</button>
        <div id="loginResult" class="result"></div>
    </div>

    <!-- 主内容区域 -->
    <div id="mainContent" class="hidden">
        <div class="section">
            <h2>任务管理</h2>
            <button onclick="loadTasks()">刷新任务列表</button>
            <button onclick="createTask()">新建任务</button>
            <div id="tasksResult" class="result"></div>
            <div id="tasksGrid" class="tasks-grid"></div>
        </div>

        <div class="section">
            <h2>认证状态</h2>
            <button onclick="checkAuthStatus()">检查认证状态</button>
            <div id="authStatusResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // 模拟认证状态
        let authState = {
            token: null,
            user: null,
            isAuthenticated: false
        };

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        function updateUI() {
            const isAuth = authState.isAuthenticated;
            
            document.getElementById('loginForm').className = isAuth ? 'login-form hidden' : 'login-form';
            document.getElementById('mainContent').className = isAuth ? '' : 'hidden';
            document.getElementById('loginBtn').className = isAuth ? 'hidden' : '';
            document.getElementById('logoutBtn').className = isAuth ? 'danger' : 'danger hidden';
            
            if (isAuth) {
                document.getElementById('userInfo').textContent = `欢迎，${authState.user.username}`;
                document.getElementById('userInfo').className = '';
            } else {
                document.getElementById('userInfo').className = 'hidden';
            }
        }

        function showLogin() {
            authState.isAuthenticated = false;
            updateUI();
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                showResult('loginResult', '正在登录...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        remember_me: false
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 保存认证状态
                    authState.token = data.access_token;
                    authState.user = data.user;
                    authState.isAuthenticated = true;
                    
                    localStorage.setItem('access_token', data.access_token);
                    
                    showResult('loginResult', '登录成功！', 'success');
                    
                    // 更新UI
                    updateUI();
                    
                    // 自动加载任务列表
                    setTimeout(loadTasks, 500);
                } else {
                    const error = await response.text();
                    showResult('loginResult', `登录失败: ${response.status} ${error}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `登录错误: ${error.message}`, 'error');
            }
        }

        function logout() {
            authState.token = null;
            authState.user = null;
            authState.isAuthenticated = false;
            localStorage.removeItem('access_token');
            updateUI();
        }

        async function loadTasks() {
            try {
                showResult('tasksResult', '正在加载任务列表...', 'info');
                
                const token = localStorage.getItem('access_token');
                const headers = {};
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch(`${API_BASE}/tasks/`, {
                    headers: headers
                });
                
                if (response.ok) {
                    const tasks = await response.json();
                    showResult('tasksResult', `加载成功，共 ${tasks.length} 个任务`, 'success');
                    
                    // 显示任务列表
                    const tasksGrid = document.getElementById('tasksGrid');
                    tasksGrid.innerHTML = '';
                    
                    tasks.forEach(task => {
                        const taskCard = document.createElement('div');
                        taskCard.className = 'task-card';
                        taskCard.innerHTML = `
                            <h3>${task.name}</h3>
                            <p>${task.description || '暂无描述'}</p>
                            <span class="task-status status-${task.status}">${getStatusText(task.status)}</span>
                            <p><small>创建时间: ${new Date(task.created_at).toLocaleString()}</small></p>
                        `;
                        tasksGrid.appendChild(taskCard);
                    });
                } else {
                    const error = await response.text();
                    showResult('tasksResult', `加载失败: ${response.status} ${error}`, 'error');
                }
            } catch (error) {
                showResult('tasksResult', `加载错误: ${error.message}`, 'error');
            }
        }

        function getStatusText(status) {
            const statusMap = {
                pending: '待处理',
                processing: '处理中',
                completed: '已完成',
                failed: '失败',
                paused: '暂停'
            };
            return statusMap[status] || status;
        }

        async function createTask() {
            const name = prompt('请输入任务名称:');
            if (!name) return;
            
            const description = prompt('请输入任务描述 (可选):') || '';
            
            try {
                const token = localStorage.getItem('access_token');
                const response = await fetch(`${API_BASE}/tasks/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        name: name,
                        description: description
                    })
                });
                
                if (response.ok) {
                    showResult('tasksResult', '任务创建成功！', 'success');
                    loadTasks(); // 重新加载任务列表
                } else {
                    const error = await response.text();
                    showResult('tasksResult', `创建失败: ${response.status} ${error}`, 'error');
                }
            } catch (error) {
                showResult('tasksResult', `创建错误: ${error.message}`, 'error');
            }
        }

        function checkAuthStatus() {
            const token = localStorage.getItem('access_token');
            const result = {
                hasToken: !!token,
                tokenPreview: token ? token.substring(0, 50) + '...' : null,
                authState: authState,
                localStorage: {
                    access_token: token ? 'exists' : 'not found'
                }
            };
            
            showResult('authStatusResult', JSON.stringify(result, null, 2), 'info');
        }

        // 页面加载时检查认证状态
        window.onload = function() {
            const token = localStorage.getItem('access_token');
            if (token) {
                // 模拟从 token 恢复认证状态
                authState.token = token;
                authState.isAuthenticated = true;
                // 这里应该获取用户信息，但为了简化，我们使用模拟数据
                authState.user = { username: 'admin' };
            }
            updateUI();
        };
    </script>
</body>
</html>
