<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>认证调试页面</h1>
    
    <div class="section">
        <h2>1. 检查当前状态</h2>
        <button onclick="checkCurrentState()">检查当前状态</button>
        <div id="currentStateResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>2. 测试登录</h2>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>3. 测试任务列表</h2>
        <button onclick="testTasks()">测试任务列表</button>
        <div id="tasksResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>4. 清除认证状态</h2>
        <button onclick="clearAuth()">清除认证状态</button>
        <div id="clearResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }
        
        function checkCurrentState() {
            const token = localStorage.getItem('access_token');
            const result = {
                hasToken: !!token,
                tokenPreview: token ? token.substring(0, 50) + '...' : null,
                tokenLength: token ? token.length : 0
            };
            
            showResult('currentStateResult', JSON.stringify(result, null, 2), 'info');
        }
        
        async function testLogin() {
            try {
                showResult('loginResult', '正在登录...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456',
                        remember_me: false
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 保存 token
                    localStorage.setItem('access_token', data.access_token);
                    
                    const result = {
                        success: true,
                        tokenPreview: data.access_token.substring(0, 50) + '...',
                        user: data.user.username,
                        savedToLocalStorage: !!localStorage.getItem('access_token')
                    };
                    
                    showResult('loginResult', JSON.stringify(result, null, 2), 'success');
                } else {
                    const error = await response.text();
                    showResult('loginResult', `登录失败: ${response.status} ${error}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `登录错误: ${error.message}`, 'error');
            }
        }
        
        async function testTasks() {
            try {
                showResult('tasksResult', '正在访问任务列表...', 'info');
                
                const token = localStorage.getItem('access_token');
                const headers = {};
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch(`${API_BASE}/tasks/`, {
                    headers: headers
                });
                
                const result = {
                    status: response.status,
                    hasToken: !!token,
                    tokenPreview: token ? token.substring(0, 20) + '...' : null,
                    hasAuthHeader: !!headers['Authorization']
                };
                
                if (response.ok) {
                    const data = await response.json();
                    result.success = true;
                    result.taskCount = data.length;
                    showResult('tasksResult', JSON.stringify(result, null, 2), 'success');
                } else {
                    const error = await response.text();
                    result.success = false;
                    result.error = error;
                    showResult('tasksResult', JSON.stringify(result, null, 2), 'error');
                }
            } catch (error) {
                showResult('tasksResult', `访问错误: ${error.message}`, 'error');
            }
        }
        
        function clearAuth() {
            localStorage.removeItem('access_token');
            showResult('clearResult', '认证状态已清除', 'success');
        }
        
        // 页面加载时检查状态
        window.onload = function() {
            checkCurrentState();
        };
    </script>
</body>
</html>
