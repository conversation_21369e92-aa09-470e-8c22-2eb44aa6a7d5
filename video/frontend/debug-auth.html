<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover { background-color: #0056b3; }
        input {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>前端认证调试页面</h1>
    
    <div class="section">
        <h3>1. 检查认证状态</h3>
        <button onclick="checkAuthStatus()">检查认证状态</button>
        <div id="authStatusResult" class="result"></div>
    </div>

    <div class="section">
        <h3>2. 登录测试</h3>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="admin123">
        <button onclick="testLogin()">登录</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <h3>3. 测试带认证的请求</h3>
        <button onclick="testAuthenticatedRequest()">获取任务列表</button>
        <div id="requestResult" class="result"></div>
    </div>

    <div class="section">
        <h3>4. 检查请求头</h3>
        <button onclick="testRequestHeaders()">测试请求头</button>
        <div id="headersResult" class="result"></div>
    </div>

    <script>
        const API_BASE = '/api/v1';

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        function checkAuthStatus() {
            const token = localStorage.getItem('access_token');
            const result = {
                hasToken: !!token,
                tokenPreview: token ? token.substring(0, 50) + '...' : null,
                tokenLength: token ? token.length : 0,
                localStorage: {
                    access_token: token ? 'exists' : 'not found'
                }
            };
            
            showResult('authStatusResult', JSON.stringify(result, null, 2), 'info');
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('loginResult', '请输入用户名和密码', 'error');
                return;
            }
            
            try {
                showResult('loginResult', '正在登录...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        remember_me: false
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 保存认证状态
                    localStorage.setItem('access_token', data.access_token);
                    
                    showResult('loginResult', `登录成功！\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const error = await response.text();
                    showResult('loginResult', `登录失败: ${response.status} ${error}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `登录错误: ${error.message}`, 'error');
            }
        }

        async function testAuthenticatedRequest() {
            try {
                showResult('requestResult', '正在发送请求...', 'info');
                
                const token = localStorage.getItem('access_token');
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch(`${API_BASE}/tasks/`, {
                    headers: headers
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('requestResult', `请求成功！\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const error = await response.text();
                    showResult('requestResult', `请求失败: ${response.status} ${error}`, 'error');
                }
            } catch (error) {
                showResult('requestResult', `请求错误: ${error.message}`, 'error');
            }
        }

        async function testRequestHeaders() {
            try {
                showResult('headersResult', '正在测试请求头...', 'info');
                
                const token = localStorage.getItem('access_token');
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                // 发送到一个会返回请求头信息的端点（如果有的话）
                // 这里我们只是显示我们设置的头信息
                const result = {
                    headers: headers,
                    token: token ? 'present' : 'missing',
                    authHeader: headers['Authorization'] ? 'set' : 'not set'
                };
                
                showResult('headersResult', JSON.stringify(result, null, 2), 'info');
            } catch (error) {
                showResult('headersResult', `测试错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查认证状态
        window.onload = function() {
            checkAuthStatus();
        };
    </script>
</body>
</html>
