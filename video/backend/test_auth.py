#!/usr/bin/env python3
"""
测试认证和API访问
"""

import requests
import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append('.')

from app.core.auth import AuthService
from app.core.config import settings

def test_login_and_api():
    """测试登录和API访问"""
    base_url = "http://localhost:8000"
    
    print("=== 测试登录 ===")
    
    # 1. 测试登录
    login_data = {
        "username": "admin",
        "password": "admin123456"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
        print(f"登录响应状态码: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            login_result = response.json()
            access_token = login_result.get("access_token")
            print(f"获取到token: {access_token[:50]}...")
            
            # 2. 测试验证token
            print("\n=== 测试token验证 ===")
            headers = {"Authorization": f"Bearer {access_token}"}
            
            verify_response = requests.get(f"{base_url}/api/v1/auth/verify-token", headers=headers)
            print(f"Token验证响应状态码: {verify_response.status_code}")
            print(f"Token验证响应内容: {verify_response.text}")
            
            # 3. 测试访问tasks API
            print("\n=== 测试访问tasks API ===")
            tasks_response = requests.get(f"{base_url}/api/v1/tasks/", headers=headers)
            print(f"Tasks API响应状态码: {tasks_response.status_code}")
            print(f"Tasks API响应内容: {tasks_response.text}")
            
            # 4. 测试不带token访问
            print("\n=== 测试不带token访问 ===")
            no_token_response = requests.get(f"{base_url}/api/v1/tasks/")
            print(f"无token访问响应状态码: {no_token_response.status_code}")
            print(f"无token访问响应内容: {no_token_response.text}")
            
        else:
            print("登录失败")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

def test_token_creation():
    """测试token创建和验证"""
    print("\n=== 测试本地token创建和验证 ===")
    
    # 创建测试token
    test_payload = {"sub": "1", "username": "admin"}
    token = AuthService.create_access_token(test_payload)
    print(f"创建的token: {token[:50]}...")
    
    # 验证token
    try:
        decoded = AuthService.verify_token(token)
        print(f"Token验证成功: {decoded}")
    except Exception as e:
        print(f"Token验证失败: {e}")

if __name__ == "__main__":
    test_token_creation()
    test_login_and_api()
