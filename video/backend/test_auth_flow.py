#!/usr/bin/env python3
"""
测试认证流程
"""

import requests
import json

def test_auth_flow():
    """测试完整的认证流程"""
    base_url = "http://localhost:8000"
    
    print("=== 测试认证流程 ===")
    
    try:
        # 1. 测试登录
        print("\n1. 测试登录...")
        login_data = {
            "username": "admin",
            "password": "admin123456",
            "remember_me": False
        }
        
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
        print(f"登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            login_result = response.json()
            access_token = login_result.get("access_token")
            print(f"登录成功，获取到token: {access_token[:50]}...")
            
            headers = {"Authorization": f"Bearer {access_token}"}
            
            # 2. 测试获取用户信息
            print("\n2. 测试获取用户信息...")
            me_response = requests.get(f"{base_url}/api/v1/auth/me", headers=headers)
            print(f"获取用户信息响应状态码: {me_response.status_code}")
            if me_response.status_code == 200:
                user_info = me_response.json()
                print(f"用户信息: {user_info['username']}")
            else:
                print(f"获取用户信息失败: {me_response.text}")
            
            # 3. 测试验证token
            print("\n3. 测试验证token...")
            verify_response = requests.get(f"{base_url}/api/v1/auth/verify-token", headers=headers)
            print(f"Token验证响应状态码: {verify_response.status_code}")
            if verify_response.status_code == 200:
                verify_result = verify_response.json()
                print(f"Token验证结果: {verify_result}")
            else:
                print(f"Token验证失败: {verify_response.text}")
            
            # 4. 测试访问tasks API
            print("\n4. 测试访问tasks API...")
            tasks_response = requests.get(f"{base_url}/api/v1/tasks/", headers=headers)
            print(f"Tasks API响应状态码: {tasks_response.status_code}")
            if tasks_response.status_code == 200:
                tasks = tasks_response.json()
                print(f"Tasks API访问成功，任务数量: {len(tasks)}")
            else:
                print(f"Tasks API访问失败: {tasks_response.text}")
            
            # 5. 测试不带token访问
            print("\n5. 测试不带token访问...")
            no_token_response = requests.get(f"{base_url}/api/v1/tasks/")
            print(f"无token访问响应状态码: {no_token_response.status_code}")
            print(f"无token访问响应内容: {no_token_response.text}")
            
        else:
            print(f"登录失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    test_auth_flow()
