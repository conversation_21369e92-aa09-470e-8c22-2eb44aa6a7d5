<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端认证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>前端认证测试</h1>
    
    <div class="section">
        <h2>1. 登录测试</h2>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>2. Token验证</h2>
        <button onclick="testTokenVerify()">验证Token</button>
        <div id="tokenResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>3. 访问Tasks API</h2>
        <button onclick="testTasksAPI()">访问Tasks</button>
        <div id="tasksResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>4. 当前Token状态</h2>
        <button onclick="checkTokenStatus()">检查Token</button>
        <div id="tokenStatus" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = '正在登录...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('access_token', data.access_token);
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `登录成功！\nToken: ${data.access_token.substring(0, 50)}...\n用户: ${data.user.username}`;
                } else {
                    const error = await response.text();
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `登录失败: ${response.status} ${error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `登录错误: ${error.message}`;
            }
        }
        
        async function testTokenVerify() {
            const resultDiv = document.getElementById('tokenResult');
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '没有找到Token，请先登录';
                return;
            }
            
            resultDiv.textContent = '正在验证Token...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/verify-token`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `Token验证成功！\n有效性: ${data.valid}\n用户: ${data.user?.username}`;
                } else {
                    const error = await response.text();
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Token验证失败: ${response.status} ${error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `验证错误: ${error.message}`;
            }
        }
        
        async function testTasksAPI() {
            const resultDiv = document.getElementById('tasksResult');
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '没有找到Token，请先登录';
                return;
            }
            
            resultDiv.textContent = '正在访问Tasks API...';
            
            try {
                const response = await fetch(`${API_BASE}/tasks/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `Tasks API访问成功！\n任务数量: ${data.length}\n数据: ${JSON.stringify(data, null, 2)}`;
                } else {
                    const error = await response.text();
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Tasks API访问失败: ${response.status} ${error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `访问错误: ${error.message}`;
            }
        }
        
        function checkTokenStatus() {
            const resultDiv = document.getElementById('tokenStatus');
            const token = localStorage.getItem('access_token');
            
            if (token) {
                try {
                    // 解析JWT payload (不验证签名，仅用于显示)
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const now = new Date();
                    
                    resultDiv.className = 'result';
                    resultDiv.textContent = `Token状态:\n存在: 是\n用户ID: ${payload.sub}\n用户名: ${payload.username}\n过期时间: ${exp.toLocaleString()}\n是否过期: ${now > exp ? '是' : '否'}\nToken: ${token.substring(0, 50)}...`;
                } catch (error) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Token解析错误: ${error.message}`;
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Token状态: 不存在';
            }
        }
        
        // 页面加载时检查token状态
        window.onload = function() {
            checkTokenStatus();
        };
    </script>
</body>
</html>
